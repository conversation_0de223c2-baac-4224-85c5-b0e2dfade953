import React, { useRef, useEffect } from 'react';
import { useGameStore } from '../../../store';
import { usePixiSlotAnimations } from '../../../hooks/usePixiSlotAnimations';

interface PixiSlotPreviewProps {
  className?: string;
  onSpin?: () => void;
}

const PixiSlotPreview: React.FC<PixiSlotPreviewProps> = ({
  className = '',
  onSpin
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { config } = useGameStore();
  const [error, setError] = useState<string | null>(null);
  
  // Get grid configuration
  const reels = config.reels?.layout?.reels || 5;
  const rows = config.reels?.layout?.rows || 3;
  const symbols = config.theme?.generated?.symbols || [];

  // Initialize GSAP + PixiJS animations with error handling
  let triggerSpin: ((winType?: string) => void) | undefined;
  let isReady = false;
  
  try {
    const result = usePixiSlotAnimations({
      containerRef,
      reels,
      rows,
      symbols
    });
    triggerSpin = result.triggerSpin;
    isReady = result.isReady;
  } catch (err) {
    console.error('PixiJS initialization error:', err);
    setError(err instanceof Error ? err.message : 'Unknown PixiJS error');
  }

  // Handle spin button clicks
  const handleSpin = () => {
    console.log('🎰 Spin button clicked - triggering PixiJS animation');
    
    // Trigger random win for demo
    const winTypes = ['small-win', 'big-win', 'mega-win', 'freespins'];
    const randomWin = winTypes[Math.floor(Math.random() * winTypes.length)];
    
    triggerSpin(randomWin);
    
    if (onSpin) {
      onSpin();
    }
  };

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* PixiJS Canvas Container */}
      <div 
        ref={containerRef}
        className="w-full h-full rounded-lg overflow-hidden"
        style={{ minHeight: '400px' }}
      />
      
      {/* Error Display */}
      {error && (
        <div className="absolute inset-0 bg-red-900/80 flex items-center justify-center">
          <div className="text-center p-6">
            <div className="text-red-300 text-6xl mb-4">⚠️</div>
            <p className="text-red-200 font-bold mb-2">PixiJS Error</p>
            <p className="text-red-300 text-sm mb-4">{error}</p>
            <p className="text-xs text-red-400">Switch to CSS mode in the header</p>
          </div>
        </div>
      )}
      
      {/* Loading Overlay */}
      {!error && !isReady && (
        <div className="absolute inset-0 bg-[#041022] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-blue-300">Initializing Professional Slot Engine...</p>
            <p className="text-xs text-gray-400 mt-2">PixiJS + GSAP Powered</p>
          </div>
        </div>
      )}
      
      {/* Spin Button Overlay */}
      {isReady && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={handleSpin}
            className="bg-gradient-to-r from-red-600 to-orange-600 text-white px-6 py-3 rounded-full font-bold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center space-x-2"
          >
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
            <span>SPIN</span>
          </button>
        </div>
      )}
      
      {/* Debug Info */}
      {isReady && process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 left-4 bg-black/70 text-white p-2 rounded text-xs">
          <div>PixiJS Slot: {reels}×{rows}</div>
          <div>Symbols: {symbols.length}</div>
          <div>GSAP: Ready</div>
        </div>
      )}
    </div>
  );
};

export default PixiSlotPreview;