/**
 * Test script for multi-image generation functionality
 * This tests the new separate element generation approach
 */

import { enhancedOpenaiClient } from './utils/enhancedOpenaiClient.js';

async function testMultiImageGeneration() {
  console.log('🧪 Testing Multi-Image Generation...');
  
  try {
    // Test 1: Generate WILD symbol with separate elements
    console.log('\n📝 Test 1: Generating WILD symbol with separate elements');
    
    const wildResult = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: 'Golden crown symbol for casino slot machine',
      generateSeparateElements: true,
      textContent: 'WILD',
      symbolDescription: 'Golden crown with jewels',
      layoutTemplate: 'text-bottom',
      onProgress: (progress) => {
        console.log(`Progress: ${progress}%`);
      }
    });
    
    if (wildResult.success && wildResult.metadata?.separateElements) {
      const { letters, symbol } = wildResult.metadata.separateElements;
      console.log('✅ WILD generation successful!');
      console.log(`   Letters generated: ${letters.length} (${letters.map(l => l.letter).join(', ')})`);
      console.log(`   Symbol generated: ${symbol.description}`);
      console.log(`   Total images: ${wildResult.images?.length || 0}`);
      
      // Verify each letter was generated
      const expectedLetters = ['W', 'I', 'L', 'D'];
      const generatedLetters = letters.map(l => l.letter);
      const allLettersGenerated = expectedLetters.every(letter => generatedLetters.includes(letter));
      
      if (allLettersGenerated) {
        console.log('✅ All WILD letters generated correctly');
      } else {
        console.log('❌ Missing letters:', expectedLetters.filter(l => !generatedLetters.includes(l)));
      }
    } else {
      console.log('❌ WILD generation failed:', wildResult.error);
    }
    
    // Test 2: Generate SCATTER symbol with separate elements
    console.log('\n📝 Test 2: Generating SCATTER symbol with separate elements');
    
    const scatterResult = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: 'Magical crystal ball for casino slot machine',
      generateSeparateElements: true,
      textContent: 'SCATTER',
      symbolDescription: 'Magical crystal ball with sparkles',
      layoutTemplate: 'text-top',
      onProgress: (progress) => {
        console.log(`Progress: ${progress}%`);
      }
    });
    
    if (scatterResult.success && scatterResult.metadata?.separateElements) {
      const { letters, symbol } = scatterResult.metadata.separateElements;
      console.log('✅ SCATTER generation successful!');
      console.log(`   Letters generated: ${letters.length} (${letters.map(l => l.letter).join(', ')})`);
      console.log(`   Symbol generated: ${symbol.description}`);
      console.log(`   Total images: ${scatterResult.images?.length || 0}`);
      
      // Verify each letter was generated (SCATTER has duplicate T's)
      const expectedLetters = ['S', 'C', 'A', 'T', 'T', 'E', 'R'];
      const generatedLetters = letters.map(l => l.letter);
      
      if (generatedLetters.length === expectedLetters.length) {
        console.log('✅ All SCATTER letters generated correctly');
      } else {
        console.log('❌ Letter count mismatch. Expected:', expectedLetters.length, 'Got:', generatedLetters.length);
      }
    } else {
      console.log('❌ SCATTER generation failed:', scatterResult.error);
    }
    
    // Test 3: Test traditional single image generation (symbol-only)
    console.log('\n📝 Test 3: Testing traditional single image generation');
    
    const symbolOnlyResult = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: 'Premium diamond gem for casino slot machine, sparkling with rainbow colors',
      generateSeparateElements: false
    });
    
    if (symbolOnlyResult.success && symbolOnlyResult.images) {
      console.log('✅ Symbol-only generation successful!');
      console.log(`   Images generated: ${symbolOnlyResult.images.length}`);
      console.log('   This should be a single combined image');
    } else {
      console.log('❌ Symbol-only generation failed:', symbolOnlyResult.error);
    }
    
    console.log('\n🎉 Multi-image generation tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Animation test function
function testAnimationCapabilities() {
  console.log('\n🎬 Testing Animation Capabilities...');
  
  // Simulate sprite data from multi-image generation
  const mockSprites = [
    { id: 'letter_W_0', type: 'letter', letter: 'W', x: 50, y: 120 },
    { id: 'letter_I_1', type: 'letter', letter: 'I', x: 110, y: 120 },
    { id: 'letter_L_2', type: 'letter', letter: 'L', x: 170, y: 120 },
    { id: 'letter_D_3', type: 'letter', letter: 'D', x: 230, y: 120 },
    { id: 'symbol_main', type: 'symbol', x: 125, y: 20 }
  ];
  
  console.log('📊 Mock sprite data created:');
  mockSprites.forEach(sprite => {
    console.log(`   ${sprite.id} (${sprite.type}) at (${sprite.x}, ${sprite.y})`);
  });
  
  // Test letter filtering (for wave animation)
  const letterSprites = mockSprites.filter(sprite => sprite.type === 'letter');
  console.log(`✅ Letter filtering works: ${letterSprites.length} letters found`);
  
  // Test symbol filtering (for symbol-specific animations)
  const symbolSprites = mockSprites.filter(sprite => sprite.type === 'symbol');
  console.log(`✅ Symbol filtering works: ${symbolSprites.length} symbols found`);
  
  console.log('🎬 Animation capability tests passed!');
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  testMultiImageGeneration().then(() => {
    testAnimationCapabilities();
  });
} else {
  // Browser environment
  console.log('🌐 Browser environment detected. Tests can be run manually.');
  window.testMultiImageGeneration = testMultiImageGeneration;
  window.testAnimationCapabilities = testAnimationCapabilities;
}

export { testMultiImageGeneration, testAnimationCapabilities };
